from fastapi import FastAPI
from .routes.auth_routes import router as auth_router

# Tạo FastAPI app instance
app = FastAPI(
    title="Auth Service API",
    description="Microservice cho xác thực người dùng",
    version="1.0.0"
)

# Đăng ký routes
app.include_router(auth_router)

# Root endpoint để test
@app.get("/")
async def root():
    return {"message": "Auth Service đang hoạt động!"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "auth-service"}
